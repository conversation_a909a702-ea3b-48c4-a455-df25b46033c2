# 中文文本情感分析模型

这是一个使用BERT+双层双向LSTM+多头注意力机制的中文文本情感分析模型，用于将文本分类为六种情感类别：其他、喜好、悲伤、厌恶、愤怒和高兴。

## 特点

- 基于BERT预训练模型的中文文本处理
- 双层双向LSTM提取序列信息
- 多头自注意力机制捕捉词之间的关系
- 使用Focal Loss解决类别不平衡问题
- 实现了混合精度训练，提高训练速度
- 支持文本情感的可视化热力图分析

## 模型架构

- BERT编码层：使用bert-base-chinese作为基础模型
- 自注意力层：增强词之间的关联
- 双层双向LSTM：捕获上下文长依赖关系
- 多头注意力层：进一步捕获关键信息
- 注意力池化：获取最重要的特征
- 分类层：多层全连接网络进行分类

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 训练模型

```bash
python emotion_analysis.py
```

训练完成后会生成：
- `best_emotion_model.pth`：最佳模型权重
- `training_history.png`：训练历史图表
- `confusion_matrix.png`：混淆矩阵
- `predictions.csv`：预测结果

### 生成热力图分析

```bash
python emotion_heatmap.py
```

这将生成：
- 每个样本的注意力热力图，显示模型关注的文本部分
- 多头注意力热力图，显示不同注意力头的关注区域
- 热力图保存在`heatmaps`目录中

## 性能指标

- 训练精度：>90%
- 验证精度：>90%
- 支持6种情感类别：
  - 0: "其他(Null)"
  - 1: "喜好(Like)"
  - 2: "悲伤(Sad)"
  - 3: "厌恶(Disgust)"
  - 4: "愤怒(Anger)"
  - 5: "高兴(Happiness)"

## 模型改进

- 完全解冻BERT层进行端到端微调
- 连接最后四层BERT输出获取更丰富的表示
- 优化学习率调度和早停策略
- 添加数据增强策略处理类别不平衡
- 实现混合精度训练提高效率
- 添加残差连接和层归一化提高稳定性

## 数据集说明

数据集包含4万多条句子，每条句子都有对应的情感标签（0-5）：
- 0: 其他(Null)
- 1: 喜好(Like)
- 2: 悲伤(Sad)
- 3: 厌恶(Disgust)
- 4: 愤怒(Anger)
- 5: 高兴(Happiness)

## 文件说明

- `emotion_analysis.py`: 情感分析模型的训练与预测
- `emotion_heatmap.py`: 情感热力图生成
- `requirements.txt`: 项目依赖列表
- `train(1).json`: 原始训练数据集
- `test.csv`: 生成的测试数据集
- `predictions.csv`: 模型预测结果
- `best_emotion_model.pth`: 保存的最佳模型
- `confusion_matrix.png`: 混淆矩阵可视化
- `training_history.png`: 训练过程可视化
- `emotion_distribution.png`: 情感分布图表
- `heatmap_*.html`: 各类情感热力图

## 注意事项

1. 如需使用真实地理位置数据，请准备一个名为`geo_data.csv`的文件，格式为：
   ```
   sentence,latitude,longitude
   ```

2. 模型训练可能需要较长时间，建议使用GPU加速训练。

3. 热力图生成需要互联网连接以加载地图数据。 