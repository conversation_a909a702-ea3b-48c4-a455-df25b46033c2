import torch
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from transformers import BertTokenizer, BertModel
import torch.nn as nn
from emotion_analysis import BertLSTMModel, BERT_MODEL
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import seaborn as sns
import matplotlib.pyplot as plt
import json
import matplotlib as mpl
from tqdm import tqdm
import sys
import os

# 设置matplotlib参数
mpl.rcParams['font.sans-serif'] = ['SimHei']
mpl.rcParams['axes.unicode_minus'] = False
mpl.rcParams['figure.facecolor'] = 'white'
mpl.rcParams['axes.facecolor'] = 'white'
mpl.rcParams['axes.grid'] = True
mpl.rcParams['grid.color'] = '#E5E5E5'
mpl.rcParams['grid.linestyle'] = '--'
mpl.rcParams['grid.alpha'] = 0.5

# 定义配色方案
COLORS = px.colors.qualitative.Set3
EMOTION_LABELS = ["喜悦", "愤怒", "厌恶", "恐惧", "惊讶", "悲伤"]

def load_model(model_path='best_emotion_model.ckpt'):
    """Load the trained model"""
    try:
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件 {model_path} 不存在")
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = BertLSTMModel()
        
        # 加载模型
        checkpoint = torch.load(model_path, map_location=device)
        if 'state_dict' in checkpoint:
            model.load_state_dict(checkpoint['state_dict'])
        else:
            model.load_state_dict(checkpoint)
            
        model = model.to(device)
        model.eval()
        return model, device
    except Exception as e:
        print(f"加载模型时出错: {str(e)}")
        sys.exit(1)

def get_emotion_probabilities(model, text, tokenizer, device):
    """Get emotion probabilities for a given text"""
    try:
        text = text.replace(' ', '').strip()
        
        encoding = tokenizer(
            text,
            add_special_tokens=True,
            max_length=128,
            padding='max_length',
            truncation=True,
            return_tensors='pt'
        )
        
        with torch.no_grad():
            input_ids = encoding['input_ids'].to(device)
            attention_mask = encoding['attention_mask'].to(device)
            outputs = model(input_ids, attention_mask)
            probs = torch.softmax(outputs, dim=1)
        
        return probs.cpu().squeeze().numpy()
    except Exception as e:
        print(f"处理文本时出错: {str(e)}")
        return None

def evaluate_model(model, test_data, test_labels, tokenizer, device):
    """评估模型性能"""
    all_probs = []
    predictions = []
    
    print("开始评估模型...")
    try:
        # 使用tqdm创建进度条
        for text in tqdm(test_data['sentence'], desc="处理文本"):
            probs = get_emotion_probabilities(model, text, tokenizer, device)
            if probs is not None:
                all_probs.append(probs)
                predictions.append(np.argmax(probs))
            else:
                print(f"警告: 跳过处理失败的文本: {text[:50]}...")
        
        if not predictions:
            raise ValueError("没有成功处理任何文本")
        
        accuracy = accuracy_score(test_labels, predictions)
        report = classification_report(test_labels, predictions, 
                                    target_names=["喜悦", "愤怒", "厌恶", "恐惧", "惊讶", "悲伤"])
        cm = confusion_matrix(test_labels, predictions)
        
        return accuracy, report, cm, predictions, all_probs
    except Exception as e:
        print(f"评估模型时出错: {str(e)}")
        sys.exit(1)

def plot_confusion_matrix(cm, save_path='confusion_matrix.png'):
    """绘制混淆矩阵"""
    try:
        plt.figure(figsize=(12, 10))
        emotion_labels = ["喜悦", "愤怒", "厌恶", "恐惧", "惊讶", "悲伤"]
        
        # 计算百分比
        cm_percent = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis] * 100
        
        # 创建热力图
        sns.heatmap(cm_percent, annot=True, fmt='.1f', cmap='YlOrRd',
                    xticklabels=emotion_labels,
                    yticklabels=emotion_labels)
        
        plt.title('情感分类混淆矩阵 (百分比%)', fontsize=16, pad=20)
        plt.xlabel('预测标签', fontsize=12, labelpad=10)
        plt.ylabel('真实标签', fontsize=12, labelpad=10)
        
        # 调整文字大小
        plt.xticks(fontsize=10)
        plt.yticks(fontsize=10)
        
        # 添加颜色条说明
        cbar = plt.gca().collections[0].colorbar
        cbar.set_label('百分比 (%)', fontsize=10)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"混淆矩阵已保存至 {save_path}")
    except Exception as e:
        print(f"绘制混淆矩阵时出错: {str(e)}")

def create_emotion_heatmap(texts, labels=None, save_path='emotion_heatmap.html'):
    """Create an interactive heatmap of emotion probabilities"""
    try:
        model, device = load_model()
        tokenizer = BertTokenizer.from_pretrained(BERT_MODEL)
        
        # 获取每个文本的情感概率
        emotion_probs = []
        predictions = []
        
        print("生成情感热力图...")
        for text in tqdm(texts, desc="分析文本"):
            probs = get_emotion_probabilities(model, text, tokenizer, device)
            if probs is not None:
                emotion_probs.append(probs)
                predictions.append(np.argmax(probs))
        
        emotion_probs = np.array(emotion_probs)
        emotion_labels = ["喜悦", "愤怒", "厌恶", "恐惧", "惊讶", "悲伤"]
        
        # 创建更详细的文本标签
        text_labels = []
        for i, (text, pred) in enumerate(zip(texts, predictions)):
            display_text = text[:20] + '...' if len(text) > 20 else text
            if labels is not None:
                label_text = f'文本 {i+1}: {display_text}<br>真实: {emotion_labels[labels[i]]}<br>预测: {emotion_labels[pred]}'
            else:
                label_text = f'文本 {i+1}: {display_text}<br>预测: {emotion_labels[pred]}'
            text_labels.append(label_text)
        
        # 创建热力图
        fig = go.Figure(data=go.Heatmap(
            z=emotion_probs,
            x=emotion_labels,
            y=text_labels,
            colorscale='RdBu_r',
            text=np.round(emotion_probs, 3),
            texttemplate='%{text}',
            textfont={"size": 10},
            hoverongaps=False
        ))
        
        # 优化布局
        fig.update_layout(
            title={
                'text': '中文文本情感分布热力图<br><sub>概率值显示在每个单元格中</sub>',
                'y':0.95,
                'x':0.5,
                'xanchor': 'center',
                'yanchor': 'top',
                'font': {'size': 20}
            },
            xaxis_title='情感类别',
            yaxis_title='文本内容',
            height=max(600, len(texts) * 60),
            width=1000,
            font=dict(size=12),
            margin=dict(l=200, r=50, t=100, b=50)
        )
        
        # 保存交互式图表
        fig.write_html(save_path)
        print(f"热力图已保存至 {save_path}")
        
    except Exception as e:
        print(f"创建热力图时出错: {str(e)}")

def plot_emotion_distribution(labels, save_path='emotion_distribution.html'):
    """绘制交互式情感分布图"""
    try:
        counts = pd.Series(labels).value_counts().sort_index()
        percentages = (counts / len(labels) * 100).round(1)
        
        # 创建子图
        fig = make_subplots(rows=1, cols=2, specs=[[{"type": "pie"}, {"type": "bar"}]],
                           subplot_titles=('情感分布饼图', '情感分布柱状图'))
        
        # 添加饼图
        fig.add_trace(
            go.Pie(labels=EMOTION_LABELS, values=counts, hole=0.4,
                   textinfo='label+percent', marker_colors=COLORS),
            row=1, col=1
        )
        
        # 添加柱状图
        fig.add_trace(
            go.Bar(x=EMOTION_LABELS, y=counts, text=percentages.map('{:.1f}%'.format),
                   textposition='outside', marker_color=COLORS),
            row=1, col=2
        )
        
        # 更新布局
        fig.update_layout(
            title_text='情感类别分布分析',
            showlegend=False,
            height=500,
            title_x=0.5,
            title_font_size=20,
            paper_bgcolor='white',
            plot_bgcolor='rgba(0,0,0,0)'
        )
        
        # 更新柱状图y轴
        fig.update_yaxes(title_text='样本数量', row=1, col=2, gridcolor='#E5E5E5')
        
        fig.write_html(save_path)
        print(f"情感分布图已保存至 {save_path}")
    except Exception as e:
        print(f"绘制情感分布图时出错: {str(e)}")

def plot_accuracy_by_emotion(test_labels, predictions, save_path='accuracy_analysis.html'):
    """绘制交互式准确率分析图"""
    try:
        accuracies = []
        confusion = []
        for i in range(len(EMOTION_LABELS)):
            mask = np.array(test_labels) == i
            if mask.sum() > 0:
                acc = accuracy_score(np.array(test_labels)[mask], 
                                  np.array(predictions)[mask])
                accuracies.append(acc * 100)
                
                # 计算该类别的混淆情况
                pred_mask = np.array(predictions)[mask]
                confusion.append([sum(pred_mask == j) for j in range(len(EMOTION_LABELS))])
            else:
                accuracies.append(0)
                confusion.append([0] * len(EMOTION_LABELS))
        
        # 创建子图
        fig = make_subplots(rows=1, cols=2,
                           subplot_titles=('各情感类别准确率', '预测混淆热力图'),
                           specs=[[{"type": "bar"}, {"type": "heatmap"}]])
        
        # 添加准确率柱状图
        fig.add_trace(
            go.Bar(x=EMOTION_LABELS, y=accuracies,
                   text=[f'{x:.1f}%' for x in accuracies],
                   textposition='outside',
                   marker_color=COLORS,
                   showlegend=False),
            row=1, col=1
        )
        
        # 添加混淆热力图
        fig.add_trace(
            go.Heatmap(z=confusion,
                       x=EMOTION_LABELS,
                       y=EMOTION_LABELS,
                       colorscale='RdYlBu_r',
                       showscale=True),
            row=1, col=2
        )
        
        # 更新布局
        fig.update_layout(
            title_text='情感分类性能分析',
            height=600,
            title_x=0.5,
            title_font_size=20,
            paper_bgcolor='white',
            plot_bgcolor='rgba(0,0,0,0)'
        )
        
        # 更新轴标签
        fig.update_yaxes(title_text='准确率 (%)', row=1, col=1, gridcolor='#E5E5E5')
        fig.update_xaxes(title_text='情感类别', row=1, col=1)
        fig.update_xaxes(title_text='预测情感', row=1, col=2)
        fig.update_yaxes(title_text='真实情感', row=1, col=2)
        
        fig.write_html(save_path)
        print(f"准确率分析图已保存至 {save_path}")
    except Exception as e:
        print(f"绘制准确率分析图时出错: {str(e)}")

def plot_probability_distribution(all_probs, save_path='probability_analysis.html'):
    """绘制交互式概率分布分析图"""
    try:
        df = pd.DataFrame(all_probs, columns=EMOTION_LABELS)
        
        # 创建子图
        fig = make_subplots(rows=2, cols=1,
                           subplot_titles=('情感预测概率分布', '预测置信度分布'),
                           specs=[[{"type": "box"}], [{"type": "histogram"}]],
                           vertical_spacing=0.2)
        
        # 添加箱线图
        for i, emotion in enumerate(EMOTION_LABELS):
            fig.add_trace(
                go.Box(y=df[emotion], name=emotion,
                       marker_color=COLORS[i],
                       boxpoints='outliers'),
                row=1, col=1
            )
        
        # 添加置信度直方图
        confidences = np.max(all_probs, axis=1)
        fig.add_trace(
            go.Histogram(x=confidences,
                         nbinsx=50,
                         marker_color='rgba(100,149,237,0.6)',
                         marker_line_color='rgb(100,149,237)',
                         marker_line_width=1.5,
                         name='置信度分布'),
            row=2, col=1
        )
        
        # 更新布局
        fig.update_layout(
            title_text='预测概率分析',
            height=900,
            title_x=0.5,
            title_font_size=20,
            showlegend=True,
            paper_bgcolor='white',
            plot_bgcolor='rgba(0,0,0,0)'
        )
        
        # 更新轴标签和网格
        fig.update_yaxes(title_text='预测概率', row=1, col=1, gridcolor='#E5E5E5')
        fig.update_xaxes(title_text='预测置信度', row=2, col=1, gridcolor='#E5E5E5')
        fig.update_yaxes(title_text='样本数量', row=2, col=1, gridcolor='#E5E5E5')
        
        fig.write_html(save_path)
        print(f"概率分布分析图已保存至 {save_path}")
    except Exception as e:
        print(f"绘制概率分布分析图时出错: {str(e)}")

def main():
    try:
        # 检查必要文件是否存在
        required_files = ['test.csv', 'test_labels.json']
        for file in required_files:
            if not os.path.exists(file):
                raise FileNotFoundError(f"找不到必要的文件: {file}")
        
        # 加载测试数据
        print("加载数据...")
        test_data = pd.read_csv('test.csv')
        with open('test_labels.json', 'r', encoding='utf-8') as f:
            test_labels = json.load(f)
        
        # 加载模型和tokenizer
        print("加载模型...")
        model, device = load_model()
        tokenizer = BertTokenizer.from_pretrained(BERT_MODEL)
        
        # 评估模型
        accuracy, report, cm, predictions, all_probs = evaluate_model(model, test_data, test_labels, tokenizer, device)
        
        # 打印评估结果
        print("\n模型评估结果:")
        print(f"整体准确率: {accuracy:.4f}")
        print("\n分类报告:")
        print(report)
        
        # 生成所有可视化图表
        print("\n生成可视化图表...")
        plot_emotion_distribution(test_labels)
        plot_accuracy_by_emotion(test_labels, predictions)
        plot_probability_distribution(all_probs)
        
        # 创建热力图（使用前15个样本作为示例）
        sample_size = 15
        sample_texts = test_data['sentence'].head(sample_size).tolist()
        sample_labels = test_labels[:sample_size]
        create_emotion_heatmap(sample_texts, sample_labels)
        
    except Exception as e:
        print(f"程序运行出错: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
