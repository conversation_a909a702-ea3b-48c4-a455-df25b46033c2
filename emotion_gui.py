import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import torch
from transformers import BertTokenizer
from emotion_analysis import Bert<PERSON>TMModel, BERT_MODEL
from emotion_heatmap import get_emotion_probabilities, load_model
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import seaborn as sns
import threading
import time
import sys
import os

class EmotionAnalysisGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("中文文本情感分析系统")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f4f8')
        
        # 设置字体
        self.setup_fonts()
        
        try:
            # 加载模型和tokenizer
            self.load_model_and_tokenizer()
            
            # 情感标签
            self.emotion_labels = ["喜悦", "愤怒", "厌恶", "恐惧", "惊讶", "悲伤"]
            
            self.setup_gui()
        except Exception as e:
            messagebox.showerror("错误", f"程序初始化失败：{str(e)}")
            self.root.quit()
            sys.exit(1)
    
    def setup_fonts(self):
        """设置字体，包含备选字体"""
        # 中文字体列表，按优先级排序
        chinese_fonts = ['SimHei', 'Microsoft YaHei', 'WenQuanYi Micro Hei', 'NSimSun', 'Arial Unicode MS']
        
        # 测试并选择第一个可用的字体
        self.main_font = None
        for font in chinese_fonts:
            try:
                test_label = tk.Label(self.root, text="测试", font=(font, 12))
                test_label.destroy()  # 清理测试用的标签
                self.main_font = font
                print(f"使用字体: {font}")
                break
            except Exception as e:
                print(f"字体 {font} 不可用: {str(e)}")
                continue
        
        if self.main_font is None:
            print("警告：未找到合适的中文字体，将使用系统默认字体")
            self.main_font = "TkDefaultFont"
    
    def load_model_and_tokenizer(self):
        """加载模型和tokenizer"""
        try:
            print("正在加载模型...")
            self.model, self.device = load_model()
            self.tokenizer = BertTokenizer.from_pretrained(BERT_MODEL)
            print("模型加载完成！")
        except Exception as e:
            print(f"模型加载失败：{str(e)}")
            self.root.withdraw()  # 隐藏主窗口
            tk.messagebox.showerror("错误", f"模型加载失败：{str(e)}\n请确保模型文件存在且正确。")
            self.root.quit()  # 退出应用
            sys.exit(1)
    
    def setup_gui(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 创建样式
        style = ttk.Style()
        style.configure('Title.TLabel', font=(self.main_font, 24, 'bold'))
        style.configure('Subtitle.TLabel', font=(self.main_font, 12))
        style.configure('Result.TLabel', font=(self.main_font, 14))
        
        # 标题
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        title_label = ttk.Label(title_frame, text="中文文本情感分析系统",
                               style='Title.TLabel')
        title_label.pack()
        
        subtitle_label = ttk.Label(title_frame,
                                 text="基于BERT-LSTM深度学习模型",
                                 style='Subtitle.TLabel')
        subtitle_label.pack()
        
        # 创建左右分栏
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左侧输入区域
        left_frame = ttk.Frame(content_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        input_label = ttk.Label(left_frame, text="请输入要分析的文本：",
                               font=(self.main_font, 12))
        input_label.pack(anchor=tk.W, pady=(0, 5))
        
        # 文本输入框
        self.text_input = scrolledtext.ScrolledText(left_frame, wrap=tk.WORD,
                                                  width=40, height=10,
                                                  font=(self.main_font, 12))
        self.text_input.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 示例文本按钮
        self.example_button = ttk.Button(left_frame, text="插入示例文本",
                                       command=self.insert_example_text)
        self.example_button.pack(pady=(0, 10))
        
        # 分析按钮
        self.analyze_button = ttk.Button(left_frame, text="开始分析",
                                       command=self.start_analysis)
        self.analyze_button.pack(pady=(0, 10))
        
        # 清空按钮
        self.clear_button = ttk.Button(left_frame, text="清空文本",
                                     command=self.clear_text)
        self.clear_button.pack(pady=(0, 20))
        
        # 右侧结果显示区域
        right_frame = ttk.Frame(content_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        # 结果标签
        result_label = ttk.Label(right_frame, text="分析结果：",
                               font=(self.main_font, 12))
        result_label.pack(anchor=tk.W, pady=(0, 5))
        
        # 主要情感结果显示
        self.result_frame = ttk.Frame(right_frame)
        self.result_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.emotion_result_label = ttk.Label(self.result_frame,
                                           text="等待分析...",
                                           style='Result.TLabel')
        self.emotion_result_label.pack()
        
        # 创建图表区域
        self.fig = Figure(figsize=(6, 4), dpi=100)
        self.canvas = FigureCanvasTkAgg(self.fig, right_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 状态栏
        self.status_label = ttk.Label(main_frame, text="就绪",
                                    font=(self.main_font, 10))
        self.status_label.pack(side=tk.BOTTOM, pady=(10, 0))
    
    def insert_example_text(self):
        """插入示例文本"""
        example_texts = [
            "今天真是太开心了，一切都很顺利！",
            "这件事让我非常生气，太过分了！",
            "看到这么脏的环境，真是让人恶心。",
            "听说晚上要一个人走夜路，好害怕。",
            "没想到会遇到这种事，真是太意外了！",
            "失去了重要的东西，心里很难过。"
        ]
        import random
        example = random.choice(example_texts)
        self.text_input.delete('1.0', tk.END)
        self.text_input.insert('1.0', example)
    
    def clear_text(self):
        """清空输入文本"""
        self.text_input.delete('1.0', tk.END)
        self.emotion_result_label.config(text="等待分析...")
        self.fig.clear()
        self.canvas.draw()
        self.update_status("已清空")
    
    def update_status(self, message):
        self.status_label.config(text=message)
        self.root.update()
    
    def plot_emotion_probabilities(self, probs):
        self.fig.clear()
        ax = self.fig.add_subplot(111)
        
        # 创建条形图
        bars = ax.bar(self.emotion_labels, probs,
                     color=sns.color_palette("husl", len(self.emotion_labels)))
        
        # 设置标题和标签
        ax.set_title('情感概率分布', fontsize=12, pad=15,
                    fontproperties=self.main_font)
        ax.set_xlabel('情感类别', fontsize=10,
                     fontproperties=self.main_font)
        ax.set_ylabel('概率', fontsize=10)
        
        # 设置x轴标签
        ax.set_xticklabels(self.emotion_labels,
                          fontproperties=self.main_font,
                          rotation=45)
        
        # 添加概率值标签
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{height:.2%}',
                   ha='center', va='bottom',
                   fontproperties=self.main_font)
        
        # 设置y轴范围
        ax.set_ylim(0, 1)
        
        # 添加网格线
        ax.grid(True, axis='y', linestyle='--', alpha=0.7)
        
        # 调整布局
        self.fig.tight_layout()
        
        # 更新画布
        self.canvas.draw()
    
    def analyze_text(self):
        try:
            # 获取输入文本
            text = self.text_input.get("1.0", tk.END).strip()
            if not text:
                self.update_status("请输入要分析的文本！")
                return
            
            # 获取情感概率
            probs = get_emotion_probabilities(self.model, text,
                                           self.tokenizer, self.device)
            
            if probs is None:
                self.update_status("分析出错，请重试！")
                return
            
            # 获取最可能的情感
            max_prob_idx = np.argmax(probs)
            max_prob = probs[max_prob_idx]
            emotion = self.emotion_labels[max_prob_idx]
            
            # 更新结果显示
            result_text = f"主要情感：{emotion}\n置信度：{max_prob:.2%}"
            self.emotion_result_label.config(text=result_text)
            
            # 更新图表
            self.plot_emotion_probabilities(probs)
            
            self.update_status("分析完成！")
            
        except Exception as e:
            self.update_status(f"分析过程出错：{str(e)}")
    
    def start_analysis(self):
        # 禁用按钮，防止重复点击
        self.analyze_button.state(['disabled'])
        self.example_button.state(['disabled'])
        self.clear_button.state(['disabled'])
        self.update_status("正在分析...")
        
        # 在新线程中运行分析
        threading.Thread(target=self.run_analysis).start()
    
    def run_analysis(self):
        try:
            self.analyze_text()
        finally:
            # 重新启用按钮
            self.analyze_button.state(['!disabled'])
            self.example_button.state(['!disabled'])
            self.clear_button.state(['!disabled'])

def main():
    try:
        root = tk.Tk()
        app = EmotionAnalysisGUI(root)
        root.mainloop()
    except Exception as e:
        print(f"程序运行出错：{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 