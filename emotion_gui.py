import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import torch
from transformers import BertTokenizer
from emotion_analysis import Bert<PERSON>TMModel, BERT_MODEL
from emotion_heatmap import get_emotion_probabilities, load_model
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import seaborn as sns
import threading
import time
import sys
import os

class EmotionAnalysisGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("中文文本情感分析系统")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f4f8')
        
        # 设置字体
        self.setup_fonts()
        
        try:
            # 加载模型和tokenizer
            self.load_model_and_tokenizer()
            
            # 情感标签
            self.emotion_labels = ["喜悦", "愤怒", "厌恶", "恐惧", "惊讶", "悲伤"]
            
            self.setup_gui()
        except Exception as e:
            messagebox.showerror("错误", f"程序初始化失败：{str(e)}")
            self.root.quit()
            sys.exit(1)
    
    def setup_fonts(self):
        """设置字体，包含备选字体"""
        # 中文字体列表，按优先级排序
        chinese_fonts = ['SimHei', 'Microsoft YaHei', 'WenQuanYi Micro Hei', 'NSimSun', 'Arial Unicode MS']
        
        # 测试并选择第一个可用的字体
        self.main_font = None
        for font in chinese_fonts:
            try:
                test_label = tk.Label(self.root, text="测试", font=(font, 12))
                test_label.destroy()  # 清理测试用的标签
                self.main_font = font
                print(f"使用字体: {font}")
                break
            except Exception as e:
                print(f"字体 {font} 不可用: {str(e)}")
                continue
        
        if self.main_font is None:
            print("警告：未找到合适的中文字体，将使用系统默认字体")
            self.main_font = "TkDefaultFont"
    
    def load_model_and_tokenizer(self):
        """加载模型和tokenizer"""
        try:
            print("正在加载模型...")
            self.model, self.device = load_model()
            self.tokenizer = BertTokenizer.from_pretrained(BERT_MODEL)
            print("模型加载完成！")
        except Exception as e:
            print(f"模型加载失败：{str(e)}")
            self.root.withdraw()  # 隐藏主窗口
            tk.messagebox.showerror("错误", f"模型加载失败：{str(e)}\n请确保模型文件存在且正确。")
            self.root.quit()  # 退出应用
            sys.exit(1)
    
    def setup_gui(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 创建样式
        style = ttk.Style()
        style.configure('Title.TLabel', font=(self.main_font, 24, 'bold'))
        style.configure('Subtitle.TLabel', font=(self.main_font, 12))
        style.configure('Result.TLabel', font=(self.main_font, 14))
        
        # 标题
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        title_label = ttk.Label(title_frame, text="中文文本情感分析系统",
                               style='Title.TLabel')
        title_label.pack()
        
        subtitle_label = ttk.Label(title_frame,
                                 text="基于BERT-LSTM深度学习模型",
                                 style='Subtitle.TLabel')
        subtitle_label.pack()
        
        # 创建左右分栏
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左侧输入区域
        left_frame = ttk.Frame(content_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        input_label = ttk.Label(left_frame, text="请输入要分析的文本：",
                               font=(self.main_font, 12))
        input_label.pack(anchor=tk.W, pady=(0, 5))
        
        # 文本输入框
        self.text_input = scrolledtext.ScrolledText(left_frame, wrap=tk.WORD,
                                                  width=40, height=10,
                                                  font=(self.main_font, 12))
        self.text_input.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 示例文本按钮
        self.example_button = ttk.Button(left_frame, text="插入示例文本",
                                       command=self.insert_example_text)
        self.example_button.pack(pady=(0, 10))

        # 分析按钮
        self.analyze_button = ttk.Button(left_frame, text="开始分析",
                                       command=self.start_analysis)
        self.analyze_button.pack(pady=(0, 10))

        # 清空按钮
        self.clear_button = ttk.Button(left_frame, text="清空文本",
                                     command=self.clear_text)
        self.clear_button.pack(pady=(0, 10))

        # 显示详细信息的复选框
        self.show_details = tk.BooleanVar()
        self.detail_checkbox = ttk.Checkbutton(left_frame, text="显示详细分析",
                                             variable=self.show_details)
        self.detail_checkbox.pack(pady=(0, 20))
        
        # 右侧结果显示区域
        right_frame = ttk.Frame(content_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        # 结果标签
        result_label = ttk.Label(right_frame, text="分析结果：",
                               font=(self.main_font, 12))
        result_label.pack(anchor=tk.W, pady=(0, 5))
        
        # 主要情感结果显示
        self.result_frame = ttk.Frame(right_frame)
        self.result_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.emotion_result_label = ttk.Label(self.result_frame,
                                           text="等待分析...",
                                           style='Result.TLabel')
        self.emotion_result_label.pack()
        
        # 创建图表区域
        self.fig = Figure(figsize=(6, 4), dpi=100)
        self.canvas = FigureCanvasTkAgg(self.fig, right_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 状态栏
        self.status_label = ttk.Label(main_frame, text="就绪",
                                    font=(self.main_font, 10))
        self.status_label.pack(side=tk.BOTTOM, pady=(10, 0))
    
    def insert_example_text(self):
        """插入示例文本"""
        example_texts = [
            "今天真是太开心了，一切都很顺利！",
            "这件事让我非常生气，太过分了！",
            "看到这么脏的环境，真是让人恶心。",
            "听说晚上要一个人走夜路，好害怕。",
            "没想到会遇到这种事，真是太意外了！",
            "失去了重要的东西，心里很难过。"
        ]
        import random
        example = random.choice(example_texts)
        self.text_input.delete('1.0', tk.END)
        self.text_input.insert('1.0', example)
    
    def clear_text(self):
        """清空输入文本"""
        self.text_input.delete('1.0', tk.END)
        self.emotion_result_label.config(text="等待分析...")
        self.fig.clear()
        self.canvas.draw()
        self.update_status("已清空")
    
    def update_status(self, message):
        self.status_label.config(text=message)
        self.root.update()
    
    def plot_emotion_probabilities(self, probs):
        self.fig.clear()
        ax = self.fig.add_subplot(111)
        
        # 创建条形图
        bars = ax.bar(self.emotion_labels, probs,
                     color=sns.color_palette("husl", len(self.emotion_labels)))
        
        # 设置标题和标签
        ax.set_title('情感概率分布', fontsize=12, pad=15,
                    fontproperties=self.main_font)
        ax.set_xlabel('情感类别', fontsize=10,
                     fontproperties=self.main_font)
        ax.set_ylabel('概率', fontsize=10)
        
        # 设置x轴标签
        ax.set_xticklabels(self.emotion_labels,
                          fontproperties=self.main_font,
                          rotation=45)
        
        # 添加概率值标签
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{height:.2%}',
                   ha='center', va='bottom',
                   fontproperties=self.main_font)
        
        # 设置y轴范围
        ax.set_ylim(0, 1)
        
        # 添加网格线
        ax.grid(True, axis='y', linestyle='--', alpha=0.7)
        
        # 调整布局
        self.fig.tight_layout()
        
        # 更新画布
        self.canvas.draw()
    
    def analyze_text(self):
        try:
            # 获取输入文本
            text = self.text_input.get("1.0", tk.END).strip()
            if not text:
                self.update_status("请输入要分析的文本！")
                return

            # 获取情感概率
            probs = get_emotion_probabilities(self.model, text,
                                           self.tokenizer, self.device)

            if probs is None:
                self.update_status("分析出错，请重试！")
                return

            # 应用改进的预测逻辑
            adjusted_probs, emotion, confidence = self.improved_prediction(text, probs)

            # 更新结果显示
            if self.show_details.get():
                # 显示详细分析
                original_emotion = self.emotion_labels[np.argmax(probs)]
                original_confidence = np.max(probs)

                result_text = f"调整后预测：{emotion} ({confidence:.2%})\n"
                result_text += f"原始预测：{original_emotion} ({original_confidence:.2%})\n"

                # 显示调整说明
                if emotion != original_emotion:
                    result_text += "※ 基于关键词进行了调整"
                else:
                    result_text += "※ 预测结果一致"
            else:
                # 简单显示
                result_text = f"主要情感：{emotion}\n置信度：{confidence:.2%}"

            # 如果置信度较低，添加提示
            if confidence < 0.5:
                result_text += f"\n(置信度较低，可能存在多种情感)"

            self.emotion_result_label.config(text=result_text)

            # 更新图表 - 显示调整后的概率
            self.plot_emotion_probabilities(adjusted_probs)

            self.update_status("分析完成！")

        except Exception as e:
            self.update_status(f"分析过程出错：{str(e)}")

    def improved_prediction(self, text, original_probs):
        """改进的预测逻辑，考虑模型的已知局限性"""
        # 复制原始概率
        adjusted_probs = original_probs.copy()

        # 基于关键词的规则调整
        text_lower = text.lower()

        # 积极情感关键词
        positive_keywords = ['爱', '喜欢', '开心', '高兴', '快乐', '幸福', '满意', '棒', '好', '赞', '优秀', '完美', '美好']
        # 消极情感关键词
        negative_keywords = ['恨', '讨厌', '生气', '愤怒', '难过', '悲伤', '害怕', '恐惧', '恶心', '厌恶', '糟糕', '坏']
        # 惊讶关键词
        surprise_keywords = ['哇', '天哪', '没想到', '意外', '惊讶', '震惊', '不敢相信']

        # 检查关键词
        has_positive = any(keyword in text for keyword in positive_keywords)
        has_negative = any(keyword in text for keyword in negative_keywords)
        has_surprise = any(keyword in text for keyword in surprise_keywords)

        # 如果检测到明显的情感倾向，调整概率
        if has_positive and not has_negative:
            # 增强喜悦情感的概率
            adjusted_probs[0] = min(1.0, adjusted_probs[0] * 1.5)  # 喜悦
            # 降低愤怒的概率
            adjusted_probs[1] = adjusted_probs[1] * 0.5  # 愤怒

        elif has_negative and not has_positive:
            # 根据具体的负面词汇调整
            if any(word in text for word in ['生气', '愤怒', '恨']):
                adjusted_probs[1] = min(1.0, adjusted_probs[1] * 1.3)  # 愤怒
            elif any(word in text for word in ['难过', '悲伤']):
                adjusted_probs[5] = min(1.0, adjusted_probs[5] * 1.3)  # 悲伤
            elif any(word in text for word in ['害怕', '恐惧']):
                adjusted_probs[3] = min(1.0, adjusted_probs[3] * 1.3)  # 恐惧
            elif any(word in text for word in ['恶心', '厌恶', '讨厌']):
                adjusted_probs[2] = min(1.0, adjusted_probs[2] * 1.3)  # 厌恶

        if has_surprise:
            adjusted_probs[4] = min(1.0, adjusted_probs[4] * 1.2)  # 惊讶

        # 重新归一化概率
        adjusted_probs = adjusted_probs / np.sum(adjusted_probs)

        # 获取最终预测
        max_prob_idx = np.argmax(adjusted_probs)
        max_prob = adjusted_probs[max_prob_idx]
        emotion = self.emotion_labels[max_prob_idx]

        return adjusted_probs, emotion, max_prob
    
    def start_analysis(self):
        # 禁用按钮，防止重复点击
        self.analyze_button.state(['disabled'])
        self.example_button.state(['disabled'])
        self.clear_button.state(['disabled'])
        self.update_status("正在分析...")
        
        # 在新线程中运行分析
        threading.Thread(target=self.run_analysis).start()
    
    def run_analysis(self):
        try:
            self.analyze_text()
        finally:
            # 重新启用按钮
            self.analyze_button.state(['!disabled'])
            self.example_button.state(['!disabled'])
            self.clear_button.state(['!disabled'])

def main():
    try:
        root = tk.Tk()
        app = EmotionAnalysisGUI(root)
        root.mainloop()
    except Exception as e:
        print(f"程序运行出错：{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 