import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from transformers import BertTokenizer, BertModel
import pandas as pd
import json
import numpy as np
from sklearn.metrics import accuracy_score, classification_report
from tqdm import tqdm
import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint
import warnings
warnings.filterwarnings('ignore')

# Constants
MAX_LENGTH = 128
BATCH_SIZE = 32
EPOCHS = 10
LEARNING_RATE = 2e-5
NUM_CLASSES = 6
BERT_MODEL = 'bert-base-chinese'
DROPOUT_RATE = 0.3  # 增加dropout率
WEIGHT_DECAY = 0.01  # 添加权重衰减

# 情感标签说明 - 根据实际数据集调整
EMOTION_LABELS = {
    0: "喜悦",  # joy
    1: "愤怒",  # anger
    2: "厌恶",  # disgust
    3: "恐惧",  # fear
    4: "惊讶",  # surprise
    5: "悲伤"   # sadness
}

# 类别权重 - 用于处理数据不平衡
CLASS_WEIGHTS = {
    0: 0.8,   # 喜悦 (最多的类别，权重较小)
    1: 1.2,   # 愤怒
    2: 1.5,   # 厌恶
    3: 1.3,   # 恐惧
    4: 2.5,   # 惊讶 (最少的类别，权重最大)
    5: 1.8    # 悲伤
}

class EmotionDataset(Dataset):
    def __init__(self, data, tokenizer, max_length=MAX_LENGTH, is_test=False, test_labels=None):
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.is_test = is_test
        
        if is_test:
            self.texts = data['sentence'].tolist()
            self.labels = test_labels if test_labels is not None else [0] * len(self.texts)
        else:
            self.texts = [item[0] for item in data]
            self.labels = [item[1] for item in data]

    def __len__(self):
        return len(self.texts)

    def __getitem__(self, idx):
        text = str(self.texts[idx])
        # 确保中文文本被正确处理
        text = text.replace(' ', '')  # 移除多余空格
        text = text.strip()  # 移除首尾空白
        
        encoding = self.tokenizer(
            text,
            add_special_tokens=True,
            max_length=self.max_length,
            padding='max_length',
            truncation=True,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(self.labels[idx], dtype=torch.long)
        }

class BertLSTMModel(pl.LightningModule):
    def __init__(self, bert_model=BERT_MODEL, hidden_size=768, num_classes=NUM_CLASSES):
        super().__init__()
        self.bert = BertModel.from_pretrained(bert_model)
        self.lstm = nn.LSTM(hidden_size, hidden_size//2, batch_first=True, bidirectional=True, dropout=DROPOUT_RATE)
        self.dropout1 = nn.Dropout(DROPOUT_RATE)
        self.dropout2 = nn.Dropout(DROPOUT_RATE)
        self.layer_norm = nn.LayerNorm(hidden_size)
        self.classifier = nn.Linear(hidden_size, num_classes)

        # 使用加权损失函数处理类别不平衡
        class_weights = torch.tensor([CLASS_WEIGHTS[i] for i in range(num_classes)], dtype=torch.float32)
        self.criterion = nn.CrossEntropyLoss(weight=class_weights, label_smoothing=0.1)

        # 添加指标跟踪
        self.training_step_outputs = []
        self.validation_step_outputs = []

    def forward(self, input_ids, attention_mask):
        bert_output = self.bert(input_ids=input_ids, attention_mask=attention_mask)
        sequence_output = bert_output.last_hidden_state
        sequence_output = self.dropout1(sequence_output)  # 第一个dropout
        
        lstm_output, _ = self.lstm(sequence_output)
        pooled_output = torch.mean(lstm_output, dim=1)
        pooled_output = self.layer_norm(pooled_output)  # 添加层归一化
        pooled_output = self.dropout2(pooled_output)  # 第二个dropout
        logits = self.classifier(pooled_output)
        return logits

    def training_step(self, batch, batch_idx):
        input_ids = batch['input_ids']
        attention_mask = batch['attention_mask']
        labels = batch['labels']
        
        logits = self(input_ids, attention_mask)
        loss = self.criterion(logits, labels)
        
        # 计算训练准确率
        preds = torch.argmax(logits, dim=1)
        acc = (preds == labels).float().mean()
        
        self.log('train_loss', loss, prog_bar=True)
        self.log('train_acc', acc, prog_bar=True)
        
        self.training_step_outputs.append({
            'loss': loss,
            'acc': acc
        })
        
        return loss

    def on_train_epoch_end(self):
        avg_loss = torch.stack([x['loss'] for x in self.training_step_outputs]).mean()
        avg_acc = torch.stack([x['acc'] for x in self.training_step_outputs]).mean()
        
        print(f"\nEpoch {self.current_epoch} Training Metrics:")
        print(f"Average Loss: {avg_loss:.4f}")
        print(f"Average Accuracy: {avg_acc:.4f}")
        
        self.training_step_outputs.clear()

    def validation_step(self, batch, batch_idx):
        input_ids = batch['input_ids']
        attention_mask = batch['attention_mask']
        labels = batch['labels']
        
        logits = self(input_ids, attention_mask)
        loss = self.criterion(logits, labels)
        
        preds = torch.argmax(logits, dim=1)
        acc = (preds == labels).float().mean()
        
        self.log('val_loss', loss, prog_bar=True)
        self.log('val_acc', acc, prog_bar=True)
        
        self.validation_step_outputs.append({
            'val_loss': loss,
            'val_acc': acc,
            'preds': preds,
            'labels': labels
        })
        
        return {'val_loss': loss, 'val_acc': acc}

    def on_validation_epoch_end(self):
        avg_loss = torch.stack([x['val_loss'] for x in self.validation_step_outputs]).mean()
        avg_acc = torch.stack([x['val_acc'] for x in self.validation_step_outputs]).mean()
        
        # 收集所有预测和标签
        all_preds = torch.cat([x['preds'] for x in self.validation_step_outputs])
        all_labels = torch.cat([x['labels'] for x in self.validation_step_outputs])
        
        print(f"\nEpoch {self.current_epoch} Validation Metrics:")
        print(f"Average Loss: {avg_loss:.4f}")
        print(f"Average Accuracy: {avg_acc:.4f}")
        
        # 打印分类报告
        print("\nValidation Classification Report:")
        print(classification_report(all_labels.cpu().numpy(), all_preds.cpu().numpy()))
        
        self.validation_step_outputs.clear()

    def configure_optimizers(self):
        # 使用带权重衰减的AdamW优化器
        optimizer = torch.optim.AdamW(
            self.parameters(),
            lr=LEARNING_RATE,
            weight_decay=WEIGHT_DECAY
        )
        
        # 添加学习率调度器
        scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
            optimizer,
            T_0=5,  # 第一次重启的周期
            T_mult=2,  # 每次重启后周期长度的倍数
            eta_min=1e-6  # 最小学习率
        )
        
        return {
            "optimizer": optimizer,
            "lr_scheduler": {
                "scheduler": scheduler,
                "monitor": "val_loss"
            }
        }

def load_data():
    # Load training data
    with open('train(1).json', 'r', encoding='utf-8') as f:
        train_data = json.load(f)
    
    # Load test data
    test_data = pd.read_csv('test.csv')
    
    # Load test labels
    with open('test_labels.json', 'r', encoding='utf-8') as f:
        test_labels = json.load(f)
    
    return train_data, test_data, test_labels

def load_model(model_path='best_emotion_model.ckpt'):
    """Load the trained model"""
    model = BertLSTMModel()
    # 加载 PyTorch Lightning 保存的检查点
    checkpoint = torch.load(model_path)
    if 'state_dict' in checkpoint:
        # 如果是 Lightning 保存的模型
        model.load_state_dict(checkpoint['state_dict'])
    else:
        # 如果是普通的 PyTorch 模型
        model.load_state_dict(checkpoint)
    model.eval()
    return model

def predict(model, test_loader):
    model.eval()
    predictions = []
    true_labels = []
    
    with torch.no_grad():
        for batch in tqdm(test_loader, desc="预测中"):
            input_ids = batch['input_ids'].to(model.device)
            attention_mask = batch['attention_mask'].to(model.device)
            labels = batch['labels'].to(model.device)
            
            outputs = model(input_ids, attention_mask)
            _, predicted = torch.max(outputs, 1)
            
            predictions.extend(predicted.cpu().numpy())
            true_labels.extend(labels.cpu().numpy())
            
            # 计算当前批次的准确率
            batch_acc = (predicted == labels).float().mean().item()
            print(f"批次准确率: {batch_acc:.4f}")
    
    # 计算整体准确率
    total_acc = accuracy_score(true_labels, predictions)
    print(f"\n整体测试准确率: {total_acc:.4f}")
    
    # 打印详细的分类报告
    print("\n分类报告:")
    print("标签含义:")
    for label_id, label_name in EMOTION_LABELS.items():
        print(f"{label_id}: {label_name}")
    print("\n" + classification_report(true_labels, predictions))
    
    return predictions, total_acc

if __name__ == "__main__":
    print("加载预训练模型...")
    try:
        model = load_model()
        print("成功加载预训练模型！")
    except FileNotFoundError:
        print("错误：找不到预训练模型文件 'best_emotion_model.ckpt'")
        print("请先运行训练过程或确保模型文件存在。")
        exit(1)
    except Exception as e:
        print(f"加载模型时出错：{str(e)}")
        exit(1)
    
    print("加载数据...")
    _, test_data, test_labels = load_data()
    
    # Initialize tokenizer
    tokenizer = BertTokenizer.from_pretrained(BERT_MODEL)
    
    # Create test dataset and dataloader
    test_dataset = EmotionDataset(test_data, tokenizer, is_test=True, test_labels=test_labels)
    test_loader = DataLoader(
        test_dataset,
        batch_size=BATCH_SIZE,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    print("在测试集上进行预测...")
    predictions, test_accuracy = predict(model, test_loader)
    
    # 保存预测结果
    test_data['predicted_emotion'] = predictions
    test_data['emotion_label'] = test_data['predicted_emotion'].map(EMOTION_LABELS)
    test_data['accuracy'] = test_accuracy
    test_data.to_csv('predictions.csv', index=False)
    
    print(f"预测完成！最终测试准确率: {test_accuracy:.4f}")
